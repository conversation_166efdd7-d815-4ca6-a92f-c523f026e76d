using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.IO;
using System.Net.Mail;
using System.Security.Cryptography;
using System.Text;
using System.Collections;
using System.Web.Caching;
using System.Net;
using NHibernate;
using System.Globalization;
using System.Collections.Generic;
using System.Security.Cryptography.X509Certificates;
using System.Net.Security;
using iGrest.Core;
using System.Linq;
using OfficeOpenXml;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Math;

/// <summary>
/// Summary description for Ut
/// </summary>
public class Utility
{
	public Utility()
	{
		//
		// TODO: Add constructor logic here
		//
	}

	public static string GetStrTroncata(int limit, string testo)
	{

		string untaggedstring = System.Text.RegularExpressions.Regex.Replace(testo, @"<(.|\n)*?>", string.Empty);

		try
		{
			if (untaggedstring.Length > limit)
			{
				string cutted = untaggedstring.Substring(0, limit);

				int i = limit;

				while (i < untaggedstring.Length && untaggedstring[i] != ' ')
				{
					cutted += untaggedstring[i].ToString();
					i++;
				}
				return cutted;
			}
			return untaggedstring;
		}
		catch
		{
			return untaggedstring;
		}
		return untaggedstring;
	}

	public static void LogDB(Grest g, UserWeb u, string message)
	{
		try
		{
			using (var db = new iGrestEntities())
			{
				iGrest.Core.Logs l = new iGrest.Core.Logs();
				l.ID_Grest = g != null ? g.ID_Grest : default(Guid?);
				l.GrestName = g != null ? g.Name : default(string);
				l.DataInsert = DateTime.Now;
				l.Name = u != null ? u.Name : default(string);
				l.Lastname = u != null ? u.Lastname : default(string);
				l.Action = message;

				db.Logs.Add(l);
				db.SaveChanges();
			}
		}
		catch
		{

		}
	}

	public static void LogDBFunc(string pagina, string message, string source = "", string stacktrace = "", string innerEx = "", string userId = "")
	{
		try
		{
			using (var db = new iGrestEntities())
			{
				iGrest.Core.Log l = new iGrest.Core.Log();
				l.DTINSERT = DateTime.Now;
				l.innerexception = innerEx;
				l.message = message;
				l.pagina = pagina;
				l.source = source;
				l.stacktrace = stacktrace;
				l.userid = userId;
				db.Log.Add(l);
				db.SaveChanges();
			}
		}
		catch
		{

		}
	}

	public static void LogDBFunc(string pagina, string message, Exception ex, string userId = "")
	{
		try
		{
			using (var db = new iGrestEntities())
			{
				iGrest.Core.Log l = new iGrest.Core.Log();
				l.DTINSERT = DateTime.Now;
				l.innerexception = ex.InnerException == null ? string.Empty : ex.InnerException.ToString();
				l.message = message;
				l.pagina = pagina;
				l.source = ex.Source;
				l.stacktrace = ex.StackTrace;
				l.userid = userId;
				db.Log.Add(l);
				db.SaveChanges();
			}
		}
		catch
		{

		}
	}

	public static Portale GetPortal()
	{
		using (var db = new iGrestEntities())
		{
			var portale = global::Portale.GetByHost(db, HttpContext.Current.Request.Url.Host);
			if (portale == null) portale = global::Portale.GetByName(db, Utility.GetKeyWeb("DefaultPortal"));

			return portale;
		}
	}

	public static string GetKeyWeb(string value)
	{
		return ConfigurationManager.AppSettings[value];
	}

	public static string GetData()
	{
		string data = DateTime.Now.Year.ToString() + DateTime.Now.Month.ToString() + DateTime.Now.Day.ToString() +
						  DateTime.Now.Hour.ToString() + DateTime.Now.Minute.ToString() + DateTime.Now.Second.ToString();
		return data;
	}

	//public static string GetPrezzo(double prezzo)
	//{
	//    return String.Format("{0:N2}", prezzo);
	//}

	public static string GetPrezzo(decimal prezzo)
	{
		return prezzo.ToString("N2");
	}

	public static void WriteLog(string error)
	{
		string txt = DateTime.Now.ToString() + " " + error + "\r\n";
		StreamWriter objStream = File.AppendText(HttpContext.Current.Server.MapPath("~/upload/log.txt"));
		objStream.WriteLine(txt);
		objStream.Close();
	}

	public static void WriteLogStripe(string error)
	{
		string txt = DateTime.Now.ToString() + " " + error + "\r\n";
		StreamWriter objStream = File.AppendText(HttpContext.Current.Server.MapPath("~/upload/log_stripe.txt"));
		objStream.WriteLine(txt);
		objStream.Close();
	}

	public static void WriteLogSatispay(string error)
	{
		string txt = DateTime.Now.ToString() + " " + error + "\r\n";
		StreamWriter objStream = File.AppendText(HttpContext.Current.Server.MapPath("~/upload/log_satispay.txt"));
		objStream.WriteLine(txt);
		objStream.Close();
	}

	public static void WriteLogOneSignal(string error)
	{
		string txt = DateTime.Now.ToString() + " " + error + "\r\n";
		StreamWriter objStream = File.AppendText(HttpContext.Current.Server.MapPath("~/upload/log_onesignal.txt"));
		objStream.WriteLine(txt);
		objStream.Close();
	}

	public static void WriteLogBanca(string error)
	{
		string txt = DateTime.Now.ToString() + " " + error + "\r\n";
		StreamWriter objStream = File.AppendText(HttpContext.Current.Server.MapPath("~/_logs/log_banca.txt"));
		objStream.WriteLine(txt);
		objStream.Close();
	}

	/// <summary>
	/// Verifica se la stringa passata come paramentro � un numero
	/// </summary>
	/// <param name="valore">Stringa da verificare</param>
	/// <returns></returns>
	public static bool IsNum(string valore)
	{
		int Num;
		return int.TryParse(valore, out Num);
	}

	public static string GeneraPassword()
	{
		var result = string.Empty;

		string timestamp = DateTime.UtcNow.ToString("yyyy-MM-dd-HH:mm:ss.ffff",
													  System.Globalization.CultureInfo.InvariantCulture);

		result = "generapasswordutente" + timestamp;
		result = Utility.Recupera_Db_MD5(result).Substring(0, 8);
		return result;
	}

	public static string Recupera_Db_MD5(string aStringa)
	{
		var result = string.Empty;

		using (ISession session = Global.SessionFactory.OpenSession())
		{
			using (session.BeginTransaction())
			{
				string SQL = "Select master.dbo.fn_varbintohexsubstring(0, HashBytes('MD5', " + QuotedStr(aStringa) + "), 1, 0) as MD5";
				IQuery qry = session.CreateSQLQuery(SQL);
				result = (string)qry.UniqueResult<string>();
			}
		}

		return result;
	}

	public static string QuotedStr(string aTesto)
	{
		aTesto = aTesto.Replace("'", "''");
		return "'" + HttpContext.Current.Server.HtmlEncode(aTesto) + "'";
	}

	#region "UTENTE"

	public static string GetUtente()
	{
		return HttpContext.Current.Request.Cookies["SessionID"].Value;
	}

	public static void SetUtente(string id)
	{
		HttpContext.Current.Response.Cookies["SessionID"].Value = id;
		HttpContext.Current.Response.Cookies["SessionID"].Expires = DateTime.Now.AddDays(2);
	}

	public static bool CheckUtente()
	{
		bool check = true;

		if (HttpContext.Current.Request.Cookies["SessionID"] == null)
			check = false;

		return check;
	}

	public static void DeleteUtente()
	{
		HttpContext.Current.Response.Cookies["SessionID"].Expires = DateTime.Now.AddDays(-1);
	}

	/// <summary>
	/// Viene generata una password sulla base del nome e cognome dell'utente
	/// </summary>
	/// <param name="nome"></param>
	/// <param name="cognome"></param>
	public static string GeneraPassword(string nome, string cognome)
	{
		string pwd = string.Empty;
		pwd += nome.Substring(0, 2).ToLower();
		//pwd += DateTime.Now.Year.ToString();
		pwd += DateTime.Now.Day.ToString();
		pwd += DateTime.Now.Millisecond.ToString();
		pwd += cognome.Substring(0, 2).ToLower();

		return pwd;
	}

	#endregion

	#region "E-MAIL"
	private static bool isValidEmail(string email)
	{
		try
		{
			var addr = new MailAddress(email);
			return addr.Address == email;
		}
		catch
		{
			return false;
		}
	}

	public static void SendMail(string mittMail, string destMail, string oggetto, string body, string cc, string bcc, ArrayList attach)
	{
		MailMessage msg = new MailMessage();
		MailAddress mitt = new MailAddress(mittMail);

		//if (string.IsNullOrEmpty(bcc))
		//{
		//	bcc = "<EMAIL>";
		//}
		//else
		//{
		//	bcc += ";<EMAIL>";
		//}

		try
		{
			msg.From = new MailAddress(Utility.GetKeyWeb("ContactFormTo"));
			msg.Sender = new MailAddress(Utility.GetKeyWeb("ContactFormTo"));
			msg.ReplyToList.Add(mitt);

			if (!string.IsNullOrEmpty(destMail))
			{
				String[] strMail = destMail.Split(new char[] { ';' });
				if (strMail.Length > 0)
				{
					strMail = strMail.Distinct().ToArray();

					for (int i = 0; (i <= (strMail.Length - 1)); i++)
					{
						if (isValidEmail(strMail[i]))
						{
							MailAddress dettMail = new MailAddress(strMail[i]);
							msg.To.Add(dettMail);
						}
					}
				}
			}
			else
			{
				MailAddress dettMail = new MailAddress("<EMAIL>"/*Utility.GetKeyWeb("ContactFormTo")*/);
				msg.To.Add(dettMail);
			}

			if (!string.IsNullOrEmpty(cc))
			{
				String[] strCC = cc.Split(new char[] { ';' });
				if ((strCC.Length > 0))
				{
					strCC = strCC.Distinct().ToArray();

					for (int i = 0; (i <= (strCC.Length - 1)); i++)
					{
						if (isValidEmail(strCC[i]))
						{
							MailAddress ccMail = new MailAddress(strCC[i]);
							msg.CC.Add(ccMail);
						}
					}
				}
			}

			if (!string.IsNullOrEmpty(bcc))
			{
				String[] strBcc = bcc.Split(new char[] { ';' });
				if ((strBcc.Length > 0))
				{
					strBcc = strBcc.Distinct().ToArray();

					for (int i = 0; (i <= (strBcc.Length - 1)); i++)
					{
						if (isValidEmail(strBcc[i]))
						{
							MailAddress bccMail = new MailAddress(strBcc[i]);
							msg.Bcc.Add(bccMail);
						}
					}
				}
			}

			if (attach != null)
			{
				if (attach.Count > 0)
				{
					for (int i = 0; (i <= (attach.Count - 1)); i++)
					{
						//FileUpload file = (FileUpload)attach[i];
						//var strFileName = Path.GetFileName(file.PostedFile.FileName);
						//file.PostedFile.SaveAs(HttpContext.Current.Server.MapPath("~/attach/" + strFileName));
						//msg.Attachments.Add(new Attachment(HttpContext.Current.Server.MapPath("~/attach/" + strFileName)));
						msg.Attachments.Add(new Attachment((string)attach[i]));
					}
				}
			}

			if (body.Contains("<%NOREPLY>"))
			{
				if (mittMail == Utility.GetKeyWeb("ContactFormTo"))
				{
					body = body.Replace("<%NOREPLY>", "Questa e-mail proviene da una casella di posta di servizio non abilitata a ricevere messaggi.");
				}
				else
				{
					body = body.Replace("<%NOREPLY>", string.Empty);
				}
			}

			msg.Subject = oggetto;
			msg.IsBodyHtml = true;
			msg.Body = body;

			SmtpClient smtpClient = new SmtpClient();

			smtpClient.EnableSsl = bool.Parse(Utility.GetKeyWeb("SmtpSsl"));

			smtpClient.Host = Utility.GetKeyWeb("SmtpEmail");

			smtpClient.Port = Convert.ToInt32(Utility.GetKeyWeb("SmtpPort"));

			// Timeout di 30 secondi per evitare blocchi
			smtpClient.Timeout = 30000;

			var username = Utility.GetKeyWeb("SendfromEmail");

			var password = Utility.GetKeyWeb("SendfromPwd");

			smtpClient.Credentials = new NetworkCredential(username, password);

			// Log configurazione SMTP per debug (solo in caso di problemi)
			try
			{
				LogDBFunc("SendMail_Config", $"SMTP Config - Host: {smtpClient.Host}, Port: {smtpClient.Port}, SSL: {smtpClient.EnableSsl}, User: {username}, To: {destMail}", "", "", "", "");
			}
			catch { }

			//if (destMail.Contains("l.zoffoli") || destMail.Contains("luca.zoffoli") || destMail.Contains("luca_zoffoli"))
			//{
			//    smtpClient.EnableSsl = true;

			//    smtpClient.Host = "*************";

			//    smtpClient.Port = 25;

			//    username = "<EMAIL>";

			//    password = "n6pkh#rH!!Gn4KtRRKwx";

			//    smtpClient.Credentials = new NetworkCredential(username, password);

			//    using (var db = new iGrestEntities())
			//    {
			//        iGrest.Core.Log l = new iGrest.Core.Log();
			//        l.DTINSERT = DateTime.Now;
			//        l.innerexception = "";
			//        l.message = "Paramas > SSL: " + smtpClient.EnableSsl + " - " + "host: " + smtpClient.Host + " - " + "port: " + smtpClient.Port +
			//            " - " + "credentials: " + username + "/" + password + " - " + "form: " + msg.From + " - " + "sender: " + msg.Sender;
			//        l.pagina = "Iscrizione - Userweb.inviamailiscrizione destmail: " + destMail;
			//        l.source = "";
			//        l.stacktrace = "";
			//        l.userid = "";
			//        db.Log.Add(l);
			//        db.SaveChanges();
			//    }
			//}

			// Configurazione protocolli SSL/TLS sicuri (rimuovo SSL3 deprecato)
			ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12 | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls;

			// Validazione certificati più sicura - accetta solo certificati validi o con errori minori
			ServicePointManager.ServerCertificateValidationCallback =
			delegate (object s, X509Certificate certificate, X509Chain chain, SslPolicyErrors sslPolicyErrors)
			{
				// Accetta certificati validi
				if (sslPolicyErrors == SslPolicyErrors.None)
					return true;

				// Log degli errori di certificato per debug
				try
				{
					LogDBFunc("SendMail", $"SSL Certificate Warning: {sslPolicyErrors} for {certificate?.Subject}", "", "", "", "");
				}
				catch { }

				// Accetta solo errori di nome (per server con certificati wildcard o self-signed in sviluppo)
				return sslPolicyErrors == SslPolicyErrors.RemoteCertificateNameMismatch;
			};

			smtpClient.Send(msg);

		}
		catch (SmtpFailedRecipientException exc) //caso in cui l'indirizzo sia sbagliato e rifiutato dal server
		{
			using (var db = new iGrestEntities())
			{
				try
				{
					WebMatikLibrary.ExceptionUtility.LogException(exc, "Application_Handler");
				}
				catch
				{

				}

				iGrest.Core.Log l = new iGrest.Core.Log();
				l.DTINSERT = DateTime.Now;
				l.innerexception = exc.InnerException != null ? exc.InnerException.ToString() : "";
				l.message = exc.Message;
				l.pagina = "Iscrizione - Userweb.inviamailiscrizione destmail: " + destMail;
				l.source = exc.Source;
				l.stacktrace = exc.StackTrace;
				l.userid = "";
				db.Log.Add(l);
				db.SaveChanges();
			}
		}
		catch (System.FormatException ex) //caso in cui si inserisce uno username e quindi si verifica l'errore nel formato dell'indirizzo mail
		{
			using (var db = new iGrestEntities())
			{

				try
				{
					WebMatikLibrary.ExceptionUtility.LogException(ex, "Application_Handler");
				}
				catch
				{

				}

				iGrest.Core.Log l = new iGrest.Core.Log();
				l.DTINSERT = DateTime.Now;
				l.innerexception = ex.InnerException != null ? ex.InnerException.ToString() : "";
				l.message = ex.Message;
				l.pagina = "Iscrizione - Userweb.inviamailiscrizione email usata come username: " + destMail;
				l.source = ex.Source;
				l.stacktrace = ex.StackTrace;
				l.userid = "";
				db.Log.Add(l);
				db.SaveChanges();
			}
		}
		catch (Exception exce) //caso in cui si inserisce uno username e quindi si verifica l'errore nel formato dell'indirizzo mail
		{
			using (var db = new iGrestEntities())
			{
				try
				{
					WebMatikLibrary.ExceptionUtility.LogException(exce, "Application_Handler");
				}
				catch
				{

				}

				iGrest.Core.Log l = new iGrest.Core.Log();
				l.DTINSERT = DateTime.Now;
				l.innerexception = exce.InnerException != null ? exce.InnerException.ToString() : "";
				l.message = exce.Message;
				l.pagina = "Iscrizione - Userweb.inviamailiscrizione - eccezione generica: " + destMail;
				l.source = exce.Source;
				l.stacktrace = exce.StackTrace;
				l.userid = "";
				db.Log.Add(l);
				db.SaveChanges();
			}

		}

		msg.Attachments.Dispose();
	}

	/// <summary>
	/// Metodo di test per verificare la connessione SMTP
	/// </summary>
	/// <param name="testEmail">Email di test per l'invio</param>
	/// <returns>True se l'invio è riuscito, False altrimenti</returns>
	public static bool TestSmtpConnection(string testEmail)
	{
		try
		{
			using (MailMessage msg = new MailMessage())
			{
				msg.From = new MailAddress(GetKeyWeb("ContactFormTo"));
				msg.To.Add(testEmail);
				msg.Subject = "Test Connessione SMTP - " + DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss");
				msg.Body = "Questo è un test di connessione SMTP dal sistema iGrest.";
				msg.IsBodyHtml = false;

				using (SmtpClient smtp = new SmtpClient())
				{
					smtp.Host = GetKeyWeb("SmtpEmail");
					smtp.Port = Convert.ToInt32(GetKeyWeb("SmtpPort"));
					smtp.EnableSsl = bool.Parse(GetKeyWeb("SmtpSsl"));
					smtp.Timeout = 30000;
					smtp.Credentials = new NetworkCredential(
						GetKeyWeb("SendfromEmail"),
						GetKeyWeb("SendfromPwd")
					);

					// Configurazione protocolli SSL/TLS
					ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12 | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls;

					smtp.Send(msg);
				}
			}

			LogDBFunc("TestSmtpConnection", $"Test SMTP riuscito per {testEmail}", "", "", "", "");
			return true;
		}
		catch (Exception ex)
		{
			LogDBFunc("TestSmtpConnection", $"Test SMTP fallito per {testEmail}: {ex.Message}", ex, "");
			return false;
		}
	}
	#endregion

	#region "URL-REWRITING"

	/// <summary>
	/// Restituisce una url sulla base della Lang corrente
	/// </summary>
	/// <param name="pagina">Nome della pagina da inserire nell'url</param>
	/// <returns></returns>
	public static string GetUrl(string pagina)
	{
		return "/" + HttpContext.Current.Items["lang_abbrev"].ToString() + "/" + pagina;
	}

	/// <summary>
	/// Restituisce una url sulla base della Lang corrente
	/// </summary>
	/// <param name="pagina">Nome della pagina con estensione</param>
	/// <returns></returns>
	public static string GetUrlExt(string pagina)
	{
		return "/" + HttpContext.Current.Items["lang"].ToString() + "/" + pagina;
	}

	public static string UrlReplace(string inputString)
	{
		string tmpUrl;
		string charToReplaceIn;
		string charToReplaceOut;
		string[] charToReplaceInArr;
		string[] charToReplaceOutArr;
		int tmp;

		inputString = inputString.Trim();
		charToReplaceIn = "&#232;|&#8230;|&#224;|�|�|�|�|�|�|�|�|&|�|�|�|�|�|�|�|�|�|�|�|�|�|�|�|&#945;|&#946;|&#947;|&#948;|&#949;|&#950;|&#951;|&#952;|&#953;|&#954;|&#955;|&#956;|&#957;|&#958;|&#959;|&#960;|&#961;|&#963;|&#962;|&#964;|&#965;|&#966;|&#967;|&#968;|&#969;|&#940;|&#941;|&#942;|&#943;|&#970;|&#912;|&#972;|&#973;|&#971;|&#944;|&#974;|&#913;|&#914;|&#915;|&#916;|&#917;|&#918;|&#919;|&#920;|&#921;|&#922;|&#923;|&#924;|&#925;|&#926;|&#927;|&#928;|&#929;|&#931;|&#932;|&#933;|&#934;|&#935;|&#936;|&#937;|�|&#904;|&#905;|&#906;|&#938;|&#908;|&#910;|&#939;|&#911;";
		charToReplaceOut = "e||a|a||e|e|a|o|i|u|e|-|a|c|e|e|i|o|u|ss|ae|oe|ue|Ae|Oe|Ue|a|b|g|d|e|z|h|q|i|k|l|m|n|j|o|p|r|s|s|t|y|" +
		"f|x|c|w|a|e|h|i|i|i|o|u|u|u|w|A|B|G|D|E|Z|H|Q|I|K|L|M|N|J|O|P|R|S|T|Y|F|X|C|W|A|E|H|I|I|O|U|U|W";

		char[] sep = { '|' };
		charToReplaceInArr = charToReplaceIn.Split(sep);
		charToReplaceOutArr = charToReplaceOut.Split(sep);

		tmpUrl = inputString;
		for (tmp = 0; (tmp <= charToReplaceInArr.Length - 1); tmp++)
		{
			tmpUrl = tmpUrl.Replace(charToReplaceInArr[tmp], charToReplaceOutArr[tmp]);
		}
		inputString = tmpUrl;

		System.Text.RegularExpressions.MatchCollection oMatches;
		string wCode;
		string strPattern;
		System.Text.RegularExpressions.Regex objRegExp;
		tmpUrl = inputString;
		strPattern = "&(\\x23\\d+);?";
		objRegExp = new System.Text.RegularExpressions.Regex(strPattern);

		oMatches = objRegExp.Matches(tmpUrl);
		foreach (System.Text.RegularExpressions.Match oMatch in oMatches)
		{
			wCode = long.Parse(oMatch.Value.Replace("&#", "").Replace(";", "")).ToString();
			tmpUrl = tmpUrl.Replace(oMatch.Value, ((string)(wCode)));
		}
		oMatches = null;
		objRegExp = null;
		inputString = tmpUrl;

		charToReplaceIn = "�,�,�,�,�,�,�,�,�,�,�,�,�,�,�,�,�,�,�,�,�,�,�,�,�,�,�,�,�,�,�,�,�,�,�,�,�,�,�,�,�,�,�,�,�,�,�,�,�,�,�,�,�,�,�,�,�,�,�,�,�,�,&#256;,&#257;,&#258;,&#259;,&#260;,&#261;,&#262;,&#263;,&#264;,&#265;,&#266;,&#267;,&#268;,&#269;,&#270;,&#271;,&#272;,&#273;,&#274;,&#275;,&#276;,&#277;,&#278;,&#279;,&#280;,&#281;,&#282;,&#283;,&#284;,&#285;,&#286;,&#287;,&#288;,&#289;,&#290;,&#291;,&#292;,&#293;,&#294;,&#295;,&#296;,&#297;,&#298;,&#299;,&#300;,&#301;,&#302;,&#303;,&#304;,&#305;,&#306;,&#307;,&#308;,&#309;,&#310;,&#311;,&#312;,&#313;,&#314;,&#315;,&#316;,&#317;,&#318;,&#319;,&#320;,&#321;,&#322;,&#323;,&#324;,&#325;,&#326;,&#327;,&#328;,&#329;,&#330;,&#331;,&#332;,&#333;,&#334;,&#335;,&#336;,&#337;,�,�,&#340;,&#341;,&#342;,&#343;,&#344;,&#345;,&#346;,&#347;,&#348;,&#349;,&#350;,&#351;,�,�,&#354;,&#355;,&#356;,&#357;,&#358;,&#359;,&#360;,&#361;,&#362;,&#363;,&#364;,&#365;,&#366;,&#367;,&#368;,&#369;,&#370;,&#371;,&#372;,&#373;,&#374;,&#375;,�,&#377;,&#378;,&#379;,&#380;,�,�";
		charToReplaceOut = @"a,a,a,a,a,a,a,c,e,e,e,e,i,i,i,i,d,n,o,o,o,o,o,x,o,u,u,u,b,b,b,a,a,a,a,a,a,a,c,e,e,e,e,i,i,i,i,k,n,o,o,o,o,o,o,u,u,u,u,y,y,y,a,a,a,a,a,a,c,c,c,c,c,c,c,c,d,d,d,d,e,e,e,e,e,e,e,e,e,e,g,g,g,g,g,g,g,g,h,h,h,h,h,i,i,i,i,i,i,i,i,i,j,j,j,j,k,k,k,l,l,l,l,l,l,l,l,l,l,n,n,n,n,n,n,n,n,n,o,o,o,o,o,o,o,o,r,r,r,r,r,r,s,s,s,s,s,s,s,s,t,t,t,t,t,t,u,u,u,u,u,u,u,u,u,u,u,u,w,w,y,y,y,z,z,z,z,z,z";

		char[] sepVir = { ',' };
		charToReplaceInArr = charToReplaceIn.Split(sepVir);
		charToReplaceOutArr = charToReplaceOut.Split(sepVir);
		tmpUrl = inputString;
		for (tmp = 0; (tmp <= charToReplaceInArr.Length - 1); tmp++)
		{
			tmpUrl = tmpUrl.Replace(charToReplaceInArr[tmp], charToReplaceOutArr[tmp]);
		}
		inputString = tmpUrl;

		tmpUrl = inputString;
		tmpUrl = tmpUrl.Replace("/", "-");
		strPattern = "[^A-Za-z0-9 \\-]";
		objRegExp = new System.Text.RegularExpressions.Regex(strPattern);

		tmpUrl = objRegExp.Replace(tmpUrl, "-");
		objRegExp = null;
		inputString = tmpUrl;
		// Elimino Spazi
		inputString = inputString.Replace(" ", "-");
		inputString = inputString.Replace("--", "-");
		inputString = inputString.Replace("--", "-");
		inputString = inputString.Replace("---", "-");
		inputString = inputString.Replace(".", "-");
		inputString = inputString.Replace("..", "-");
		inputString = inputString.Replace("...", "-");
		inputString = inputString.Replace("....", "-");

		return inputString;

	}
	#endregion

	#region "CRIPT DECRIPT"

	private static string chiave = "AxTYQWCvGTFRbgLL";
	private static string iv = "QWExcfTyUxxLOafO";

	public static string CryptString(string S)
	{
		RijndaelManaged rjm = new RijndaelManaged();
		rjm.KeySize = 128;
		rjm.BlockSize = 128;
		rjm.Key = ASCIIEncoding.ASCII.GetBytes(chiave);
		rjm.IV = ASCIIEncoding.ASCII.GetBytes(iv);
		byte[] input = Encoding.UTF8.GetBytes(S);
		byte[] output = rjm.CreateEncryptor().TransformFinalBlock(input, 0, input.Length);

		return Convert.ToBase64String(output);
	}

	public static string DeCryptString(string S)
	{
		RijndaelManaged rjm = new RijndaelManaged();
		rjm.KeySize = 128;
		rjm.BlockSize = 128;
		rjm.Key = ASCIIEncoding.ASCII.GetBytes(chiave);
		rjm.IV = ASCIIEncoding.ASCII.GetBytes(iv);
		try
		{
			byte[] input = Convert.FromBase64String(S);
			byte[] output = rjm.CreateDecryptor().TransformFinalBlock(input, 0, input.Length);
			return Encoding.UTF8.GetString(output);
		}
		catch
		{
			return S;
		}
	}
	#endregion

	#region "CUSTOM"
	public static ArrayList GetDay()
	{
		ArrayList arr = new ArrayList();

		for (int i = 1; i < 32; i++)
		{
			var str = (i.ToString().Length == 1) ? "0" + i.ToString() : i.ToString();
			arr.Add(str);
		}
		return arr;
	}
	public static ArrayList GetMonth()
	{
		ArrayList arr = new ArrayList();

		for (int i = 1; i < 13; i++)
		{
			var str = (i.ToString().Length == 1) ? "0" + i.ToString() : i.ToString();
			arr.Add(str);
		}
		return arr;
	}
	public static ArrayList GetYear()
	{
		ArrayList arr = new ArrayList();

		for (int i = 1950; i < DateTime.Now.Year + 1; i++)
		{
			arr.Add(i.ToString());
		}
		return arr;
	}
	public static ArrayList GetEta()
	{
		ArrayList arr = new ArrayList();

		for (int i = 18; i < 86; i++)
		{
			var str = i.ToString();
			arr.Add(str);
		}
		return arr;
	}
	public static string GetNumProg(int prog)
	{
		var num = "";
		switch (prog.ToString().Length)
		{
			case 1:
				num = "00" + prog.ToString();
				break;
			case 2:
				num = "0" + prog.ToString();
				break;
			default:
				num = prog.ToString();
				break;
		}
		return num;
	}
	public static string GetNumProgRic(int prog)
	{
		var num = "";
		switch (prog.ToString().Length)
		{
			case 1:
				num = "0000" + prog.ToString();
				break;
			case 2:
				num = "000" + prog.ToString();
				break;
			case 3:
				num = "00" + prog.ToString();
				break;
			case 4:
				num = "0" + prog.ToString();
				break;
			default:
				num = prog.ToString();
				break;
		}
		return num;
	}
	public static string GetBoolValue(bool value)
	{
		string mod = "";
		switch (value.ToString())
		{
			case "True":
				mod = "Si";
				break;
			case "False":
				mod = "No";
				break;
		}
		return mod;
	}

	public static LicenseType GetLicenseTypeFromString(string licenseType)
	{
		switch (licenseType)
		{
			case "P":
				return LicenseType.Professional;
			case "L":
				return LicenseType.Lite;
			case "S":
			default:
				return LicenseType.Standard;
		}
	}

	public static string GetLicenseTypeFromEnum(LicenseType licenseType)
	{
		switch (licenseType)
		{
			case LicenseType.Professional:
				return "P";
			case LicenseType.Lite:
				return "L";
			case LicenseType.Standard:
			default:
				return "S";
		}
	}

	public static string ConvertDateFormat(string data)
	{
		var dt = Convert.ToDateTime(data);

		var hasTime = dt.Hour > 0 || dt.Minute > 0 || dt.Second > 0;

		var time = string.Empty;

		if (hasTime)
		{
			time = " " + data.Split(' ')[1];
		}
        //return Convert.ToDateTime(data).Day + "-" + Convert.ToDateTime(data).Month + "-" + Convert.ToDateTime(data).Year + time;
        //note: locale prima riga, produzione seconda
        //    if (HttpContext.Current.Request.Url.AbsoluteUri.Contains("localhost"))
        //{
        //	return Convert.ToDateTime(data).Year + "-" + Convert.ToDateTime(data).Month + "-" + Convert.ToDateTime(data).Day + time;
        //}
        //else
        //{
        //	return Convert.ToDateTime(data).Day + "-" + Convert.ToDateTime(data).Month + "-" + Convert.ToDateTime(data).Year + time;
        //}
        return Convert.ToDateTime(data).Year + "-" + Convert.ToDateTime(data).Month + "-" + Convert.ToDateTime(data).Day + time;
    }

	/// <summary>
	/// Returns the first day of the week that the specified
	/// date is in using the current culture. 
	/// </summary>
	public static DateTime GetFirstDayOfWeek(DateTime dayInWeek)
	{
		CultureInfo defaultCultureInfo = CultureInfo.CurrentCulture;
		return GetFirstDayOfWeek(dayInWeek, defaultCultureInfo);
	}
	/// <summary>
	/// Returns the first day of the week that the specified date 
	/// is in. 
	/// </summary>
	public static DateTime GetFirstDayOfWeek(DateTime dayInWeek, CultureInfo cultureInfo)
	{
		DayOfWeek firstDay = cultureInfo.DateTimeFormat.FirstDayOfWeek;
		DateTime firstDayInWeek = dayInWeek.Date;
		while (firstDayInWeek.DayOfWeek != firstDay)
			firstDayInWeek = firstDayInWeek.AddDays(-1);

		return firstDayInWeek;
	}
	#endregion

	#region "CACHE"

	public static void CacheInsert(string name, object obj)
	{
		HttpContext.Current.Cache.Insert(name, obj, null, Cache.NoAbsoluteExpiration, new TimeSpan(1, 0, 0, 0), CacheItemPriority.Normal, null);
	}

	public static void CacheRemove(string name)
	{
		HttpContext.Current.Cache.Remove(name);
	}

	public static void CacheClear()
	{
		IDictionaryEnumerator cacheEnumerator = HttpContext.Current.Cache.GetEnumerator();

		while (cacheEnumerator.MoveNext())
		{
			HttpContext.Current.Cache.Remove(cacheEnumerator.Key.ToString());
		}
	}

	#endregion

	#region IMPORT USERS
	public class ImportData
	{
		public static void ImportFromExcel(string filePath)
		{
			var file = new FileInfo(filePath);
			using (var package = new ExcelPackage(file))
			{
				//get the first worksheet in the workbook
				var worksheet = package.Workbook.Worksheets.First();
				int colCount = worksheet.Dimension.End.Column;  //get Column Count
				int rowCount = worksheet.Dimension.End.Row;     //get row count

				//columns
				var idGrestCol = 1;
				var archivioCol = 2;
				var avatarCol = 3;
				var lastnameCol = 4;
				var nameCol = 5;
				var emailCol = 6;
				var pwdCol = 7;
				var barcodeCol = 8;
				var barcodeArchiveCol = 9;
				var addressCol = 10;
				var phoneCol = 11;
				var fatherCol = 12;
				var fatherPhoneCol = 13;
				var motherCol = 14;
				var motherPhoneCol = 15;
				var grandparentPhoneCol = 16;
				var birthdayCol = 17;
				var genderCol = 18;
				var idProvinciaCol = 19;
				var idComuneCol = 20;
				var medicalInfoCol = 21;
				var medicalCareCol = 22;
				var schoolNameCol = 23;
				var schoolClassCol = 24;
				var schoolCityCol = 25;
				var sizeCol = 26;
				var noteCol = 27;
				var contoCol = 28;
				var weeksCol = 29;
				var idParentCol = 30;
				var insuranceNCol = 31;
				var parentCfCol = 32;
				var childCfCol = 33;
				var capCol = 34;
				var cittaCol = 35;
				var provinciaCol = 36;
				var cognomeGenFiscCol = 37;
				var nomeGenFiscCol = 38;
				var indirGenFiscCol = 39;
				var capGenFiscCol = 40;
				var cittaGenFiscCol = 41;
				var provGenFiscCol = 42;
				var cfGenFiscCol = 43;
				var tempoPienoParzCol = 44;
				var noteRisCol = 45;
				var limiteGiornoBarCol = 46;
				var countryCol = 47;
				var extra1Col = 48;
				var extra2Col = 49;
				var extra3Col = 50;
				var dataComunCol = 51;
				var luogoComunCol = 52;
				var dataBattCol = 53;
				var luogoBattCol = 54;
				var responsDatiCol = 55;
				var emailSecondCol = 56;
				var hqEnableCol = 57;
				var hqDefaultCol = 58;
				var battAltraParrCol = 59;
				var battFuoriCittaCol = 60;
				var battNullaOstaCol = 61;
				var dataCresimaCol = 62;
				var luogoCresimaCol = 63;
				var celebranteCresimaCol = 64;
				var nominativoPadrCol = 65;
				var nominativoMadrCol = 66;
				var indirizzoPadrCol = 67;
				var indirizzoMadrCol = 68;
				var lastloginDateCol = 69;
				var cartaIdentitaCol = 70;
				var tesseraSanitariaCol = 71;
				var cardNCol = 72;
				var battOrtodossoCol = 73;

				var province = new Provincia().Gets(0, false);
				var comuni = new Comune().Gets(0, false);

				try
				{
					var years = new List<Grest_Year>();

					using (var db = new iGrestEntities())
					{
						for (int row = 2; row <= rowCount; row++)
						{
							//values
							var idGrestStr = ExcelValue(worksheet, row, idGrestCol);

							if (string.IsNullOrEmpty(idGrestStr))
							{
								throw new Exception("ID Grest not specified");
							}

							var idGrest = new Guid(idGrestStr);

#if DEBUG
                        //idGrest = new Guid("B056A9B3-7A39-4572-BD5B-68167C38C11F");
#endif

							var archivioStr = ExcelValue(worksheet, row, archivioCol);

							if (string.IsNullOrEmpty(archivioStr))
							{
								throw new Exception("ID Year not specified");
							}

							var archivio = int.Parse(archivioStr);

#if DEBUG
                        //archivio = 802;
#endif
							var avatar = ExcelValue(worksheet, row, avatarCol);
							var lastname = ExcelValue(worksheet, row, lastnameCol);
							var name = ExcelValue(worksheet, row, nameCol);
							var email = ExcelValue(worksheet, row, emailCol);
							var pwd = ExcelValue(worksheet, row, pwdCol);

							var birthdayStr = ExcelValue(worksheet, row, birthdayCol);
							var birthday = default(DateTime?);
							if (!string.IsNullOrEmpty(birthdayStr))
							{
								DateTime val;
								if (DateTime.TryParse(birthdayStr, out val))
								{
									birthday = val;
								}
							}

							if (string.IsNullOrEmpty(pwd) && !string.IsNullOrEmpty(email))
							{
								if (!birthday.HasValue)
									pwd = CryptString(name + "_" + lastname);
								else
								{
									pwd = CryptString(name + birthday.Value.Year.ToString());
								}
							}
							else if (!string.IsNullOrEmpty(pwd))
							{
								pwd = CryptString(pwd);
							}

							var barcode = ExcelValue(worksheet, row, barcodeCol);
							var barcodeArchive = ExcelValue(worksheet, row, barcodeArchiveCol);
							var address = ExcelValue(worksheet, row, addressCol);
							var phone = ExcelValue(worksheet, row, phoneCol);
							var father = ExcelValue(worksheet, row, fatherCol);
							var fatherPhone = ExcelValue(worksheet, row, fatherPhoneCol);
							var mother = ExcelValue(worksheet, row, motherCol);
							var motherPhone = ExcelValue(worksheet, row, motherPhoneCol);
							var grandparentPhone = ExcelValue(worksheet, row, grandparentPhoneCol);

							var gender = ExcelValue(worksheet, row, genderCol);

							var idProvinciaStr = ExcelValue(worksheet, row, idProvinciaCol);
							var idProvincia = default(int?);
							if (!string.IsNullOrEmpty(idProvinciaStr))
							{
								int p = 0;
								if (!int.TryParse(idProvinciaStr, out p))
								{
									var pp = province.FirstOrDefault(x =>
										 x.Sigla.ToLower() == idProvinciaStr.ToLower()
										 || x.Nome.ToLower() == idProvinciaStr.ToLower());

									if (pp != null)
										p = pp.Id;
								}

								if (p != 0)
									idProvincia = p;
							}

							var idComuneStr = ExcelValue(worksheet, row, idComuneCol);
							var idComune = default(int?);
							if (!string.IsNullOrEmpty(idComuneStr))
							{
								int c = 0;
								if (!int.TryParse(idComuneStr, out c))
								{
									var cc = comuni.FirstOrDefault(x => x.Nome.ToLower() == idComuneStr.ToLower());

									if (cc != null)
										c = cc.Id;
								}

								if (c != 0)
									idComune = c;
							}

							var mediacalInfo = ExcelValue(worksheet, row, medicalInfoCol);
							var medicalCare = ExcelValue(worksheet, row, medicalCareCol);
							var schoolName = ExcelValue(worksheet, row, schoolNameCol);
							var schoolClass = ExcelValue(worksheet, row, schoolClassCol);
							var schoolCity = ExcelValue(worksheet, row, schoolCityCol);
							var size = ExcelValue(worksheet, row, sizeCol);
							var note = ExcelValue(worksheet, row, noteCol);

							var contoStr = ExcelValue(worksheet, row, contoCol);
							var conto = default(decimal);
							if (!string.IsNullOrEmpty(contoStr))
								conto = decimal.Parse(contoStr);

							var weeks = ExcelValue(worksheet, row, weeksCol);

							var idParentStr = ExcelValue(worksheet, row, idParentCol);
							var idParent = default(Guid?);
							if (!string.IsNullOrEmpty(idParentStr))
								idParent = new Guid(idParentStr);

							var insuranceN = ExcelValue(worksheet, row, insuranceNCol);
							var parentCf = ExcelValue(worksheet, row, parentCfCol);
							var childCf = ExcelValue(worksheet, row, childCfCol);
							var cap = ExcelValue(worksheet, row, capCol);
							var citta = ExcelValue(worksheet, row, cittaCol);
							var provincia = ExcelValue(worksheet, row, provinciaCol);
							var cognomeGenFisc = ExcelValue(worksheet, row, cognomeGenFiscCol);
							var nomeGenFisc = ExcelValue(worksheet, row, nomeGenFiscCol);
							var indirGenFisc = ExcelValue(worksheet, row, indirGenFiscCol);
							var capGenFisc = ExcelValue(worksheet, row, capGenFiscCol);
							var cittaGenFisc = ExcelValue(worksheet, row, cittaGenFiscCol);
							var provGenFisc = ExcelValue(worksheet, row, provGenFiscCol);
							var cfGenFisc = ExcelValue(worksheet, row, cfGenFiscCol);
							var tempoPienoParz = ExcelValue(worksheet, row, tempoPienoParzCol);
							var noteRis = ExcelValue(worksheet, row, noteRisCol);

							var limiteGiornoBarStr = ExcelValue(worksheet, row, limiteGiornoBarCol);
							var limiteGiornoBar = default(decimal);
							if (!string.IsNullOrEmpty(limiteGiornoBarStr))
								limiteGiornoBar = decimal.Parse(limiteGiornoBarStr);

							var country = ExcelValue(worksheet, row, countryCol);
							var extra1 = ExcelValue(worksheet, row, extra1Col);
							var extra2 = ExcelValue(worksheet, row, extra2Col);
							var extra3 = ExcelValue(worksheet, row, extra3Col);

							var dataComunStr = ExcelValue(worksheet, row, dataComunCol);
							var dataComun = default(DateTime?);
							if (!string.IsNullOrEmpty(dataComunStr))
							{
								DateTime val;
								if (DateTime.TryParse(dataComunStr, out val))
								{
									dataComun = val;
								}
							}

							var luogoComun = ExcelValue(worksheet, row, luogoComunCol);

							var dataBattStr = ExcelValue(worksheet, row, dataBattCol);
							var dataBatt = default(DateTime?);
							if (!string.IsNullOrEmpty(dataBattStr))
							{
								DateTime val;
								if (DateTime.TryParse(dataBattStr, out val))
								{
									dataBatt = val;
								}
							}

							var luogoBatt = ExcelValue(worksheet, row, luogoBattCol);

							var responsDatiStr = ExcelValue(worksheet, row, responsDatiCol);
							var responsDati = false;
							if (!string.IsNullOrEmpty(responsDatiStr))
								responsDati = responsDatiStr == "1";

							var emailSecond = ExcelValue(worksheet, row, emailSecondCol);

							var hqEnableStr = ExcelValue(worksheet, row, hqEnableCol);
							var hqEnable = default(int);
							if (!string.IsNullOrEmpty(hqEnableStr))
								hqEnable = int.Parse(hqEnableStr);

							var hqDefaultStr = ExcelValue(worksheet, row, hqDefaultCol);
							var hqDefault = false;
							if (!string.IsNullOrEmpty(hqDefaultStr))
								hqDefault = hqDefaultStr == "1";

							var battAltraParrStr = ExcelValue(worksheet, row, battAltraParrCol);
							var battAltraParr = false;
							if (!string.IsNullOrEmpty(battAltraParrStr))
								battAltraParr = battAltraParrStr == "1";

							var battFuoriCittaStr = ExcelValue(worksheet, row, battFuoriCittaCol);
							var battFuoriCitta = false;
							if (!string.IsNullOrEmpty(battFuoriCittaStr))
								battFuoriCitta = battFuoriCittaStr == "1";

							var battNullaOstaStr = ExcelValue(worksheet, row, battNullaOstaCol);
							var battNullaOsta = false;
							if (!string.IsNullOrEmpty(battNullaOstaStr))
								battNullaOsta = battNullaOstaStr == "1";

							var dataCresimaStr = ExcelValue(worksheet, row, dataCresimaCol);
							var dataCresima = default(DateTime?);
							if (!string.IsNullOrEmpty(dataCresimaStr))
							{
								DateTime val;
								if (DateTime.TryParse(dataCresimaStr, out val))
								{
									dataCresima = val;
								}
							}

							var luogoCresima = ExcelValue(worksheet, row, luogoCresimaCol);
							var celebranteCresima = ExcelValue(worksheet, row, celebranteCresimaCol);
							var nominativoPadr = ExcelValue(worksheet, row, nominativoPadrCol);
							var nominativoMadr = ExcelValue(worksheet, row, nominativoMadrCol);
							var indirizzoPadr = ExcelValue(worksheet, row, indirizzoPadrCol);
							var indirizzoMadr = ExcelValue(worksheet, row, indirizzoMadrCol);

							var lastLoginDateStr = ExcelValue(worksheet, row, lastloginDateCol);
							var lastLoginDate = default(DateTime?);
							if (!string.IsNullOrEmpty(lastLoginDateStr))
							{
								DateTime val;
								if (DateTime.TryParse(lastLoginDateStr, out val))
								{
									lastLoginDate = val;
								}
							}

							var cartaIdentita = ExcelValue(worksheet, row, cartaIdentitaCol);
							var tesseraSanitaria = ExcelValue(worksheet, row, tesseraSanitariaCol);
							var cardN = ExcelValue(worksheet, row, cardNCol);

							var battOrtodossoStr = ExcelValue(worksheet, row, battOrtodossoCol);
							var battOrtodosso = false;
							if (!string.IsNullOrEmpty(battOrtodossoStr))
								battOrtodosso = battOrtodossoStr == "1";

							var enabled = true;

							var year = db.Grest_Year.FirstOrDefault(x => x.ID_Grest == idGrest && x.ID_Year == archivio);

							if (year == null)
								year = years.FirstOrDefault(x => x.ID_Grest == idGrest && x.ID_Year == archivio);

							if (year == null)
							{
								year = new Grest_Year
								{
									Title = DateTime.Now.Year,
									Description = (DateTime.Now.Year).ToString(),
									ID_Grest = idGrest,
									Removed = false,
									StartDate = DateTime.Now.Date,
									EndDate = DateTime.Now.Date.AddYears(1)
								};

								year = db.Grest_Year.Add(year);

								years.Add(year);
							}

							var isToInsert = false;

							var uCheck = UserWeb.GetByNominativeAndEmail(name, lastname, email, idGrest, archivio);
							if (uCheck == null)
								isToInsert = true;

							if (isToInsert)
							{
								var u = new Users
								{
									ID_Year = archivio,
									ID_User = Guid.NewGuid(),
									ID_Grest = idGrest,
									Lastname = lastname,
									Name = name,
									Email = email,
									Pwd = pwd,
									Address = address,
									Phone = phone ?? string.Empty,
									Mother = mother,
									MotherPhone = motherPhone,
									Birthday = birthday,
									Gender = gender,
									ID_Provincia = idProvincia,
									ID_Comune = idComune,
									InsuranceN = insuranceN ?? string.Empty,
									ChildCf = childCf ?? string.Empty,
									ParentCf = parentCf ?? string.Empty,
									Cap = cap ?? string.Empty,
									Citta = citta ?? string.Empty,
									Provincia = provincia ?? string.Empty,
									CognomeGenitoreFiscale = cognomeGenFisc,
									NomeGenitoreFiscale = nomeGenFisc,
									IndirizzoGenitoreFiscale = indirGenFisc,
									CapGenitoreFiscale = capGenFisc,
									CfGenitoreFiscale = cfGenFisc,
									Country = country,
									Extra1 = extra1,
									Extra2 = extra2,
									Extra3 = extra3,
									EmailSecondaria = emailSecond,
									Father = father,
									FatherPhone = fatherPhone,
									Avatar = avatar ?? string.Empty,
									Barcode = barcode ?? string.Empty,
									BarcodeArchive = barcodeArchive ?? string.Empty,
									BattesimoAltraParrocchia = battAltraParr,
									BattesimoFuoriCitta = battFuoriCitta,
									BattesimoNullaOsta = battNullaOsta,
									BattesimoOrtodosso = battOrtodosso,
									CardN = cardN,
									CartaIdentita = cartaIdentita,
									CelebranteCresima = celebranteCresima,
									CittaGenitoreFiscale = cittaGenFisc,
									DataBattesimo = dataBatt,
									Conto = conto,
									DataComunione = dataComun,
									DataCresima = dataCresima,
									GrandparentsPhone = grandparentPhone,
									HeadquarterDefault = hqDefault,
									HeadquarterEnable = hqEnable,
									ID_Parent = idParent,
									IndirizzoMadrina = indirizzoMadr,
									IndirizzoPadrino = indirizzoPadr,
									LastLoginDate = lastLoginDate,
									LimiteGiornalieroBar = limiteGiornoBar,
									LuogoBattesimo = luogoBatt,
									LuogoComunione = luogoComun,
									LuogoCresima = luogoCresima,
									MedicalCare = medicalCare,
									MedicalInfo = mediacalInfo,
									NominativoMadrina = nominativoMadr,
									NominativoPadrino = nominativoPadr,
									Note = note,
									NoteRiservate = noteRis,
									ProvinciaGenitoreFiscale = provGenFisc,
									ResponsabilitaDati = responsDati,
									SchoolCity = schoolCity,
									SchoolClass = schoolClass,
									SchoolName = schoolName,
									Size = size,
									TempoPienoParziale = tempoPienoParz,
									TesseraSanitaria = tesseraSanitaria,
									Weeks = weeks,
									Rel_Users_Year = new List<Rel_Users_Year> {
													 new Rel_Users_Year
													 {
														  Grest_Year = year,
														  Enabled = enabled,
														  Removed = false,
														  ID_UserType = 4,
														  DateInsert = DateTime.Now,
														  Qualified = false
													 }
												}
								};

								db.Users.Add(u);
							}
						}

						db.SaveChanges();
					}
				}
				catch (Exception ex)
				{

				}
			}
		}

		public static string ExcelValue(ExcelWorksheet worksheet, int row, int col)
		{
			if (worksheet.Cells[row, col].Value != null)
			{
				var value = worksheet.Cells[row, col].Value.ToString().Trim();

				if (value.ToLower() == "null")
				{
					return default(string);
				}
				else
				{
					return value;
				}
			}

			return default(string);
		}
	}

	#endregion

	public class Movimenti
	{
		public List<Movimento> movimenti { get; set; }
	}

	public class Movimento
	{
		public int ID_Prodotto { get; set; }

		public decimal Price { get; set; }

		public string DescriProdotto { get; set; }
	}

	public class BalancePlus : Balance
	{
		public BalancePlus(Balance Balance)
		{
			this.ID_Balance = Balance.ID_Balance;
			this.ID_Grest = Balance.ID_Grest;
			this.ID_UserType = Balance.ID_UserType;
			this.ID_User = Balance.ID_User;
			this.ID_Type = Balance.ID_Type;
			this.ID_Year = Balance.ID_Year;
			this.DataMovimento = Balance.DataMovimento;
			this.Importo = Balance.Importo;
			this.Causale = Balance.Causale;
			this.Removed = Balance.Removed;
			this.Lastname = Balance.Lastname;
			this.Name = Balance.Name;
			this.ID_PaymentType = Balance.ID_PaymentType;
			this.ID_Headquarter = Balance.ID_Headquarter;
			this.Transaction_ID = Balance.Transaction_ID;
			this.Transaction_User_Email = Balance.Transaction_User_Email;
			this.Transaction_User_Firstname = Balance.Transaction_User_Firstname;
			this.Transaction_User_Lastname = Balance.Transaction_User_Lastname;
			this.Status = Balance.Status;
			this.ID_IncomeStatement = Balance.ID_IncomeStatement;
			this.CodiceFiscale = Balance.CodiceFiscale;
			this.SchoolClass = Balance.SchoolClass;
			this.IvaPerc = Balance.IvaPerc;
			this.Hashtags = Balance.Hashtags;
			this.Weeks = Balance.Weeks;
		}

		public string UserCreatedDenomination { get; set; }

		public string UserLastEditDenomination { get; set; }

		public string UserWeeks { get; set; }

		public string DocumentTitle { get; set; }

		public string DocumentName { get; set; }

		public bool FeXml { get; set; }
	}

	public class GrestFattViewModel
	{
		public Guid ID_GrestFatt { get; set; }
		public Guid ID_Grest { get; set; }
		public string Name { get; set; }
		public string Address { get; set; }
		public string ZipCode { get; set; }
		public int ID_Provincia { get; set; }
		public int ID_Comune { get; set; }
		public string Phone { get; set; }
		public string Fax { get; set; }
		public string Email { get; set; }
		public string PivaCf { get; set; }
		public string Sezionale { get; set; }
		public int? ID_Headquarter { get; set; }
		public string Firma { get; set; }
		public string NoteRicevuta { get; set; }
		public string ID_RegimeFiscale { get; set; }
		public string REA_Ufficio { get; set; }
		public string REA_Numero { get; set; }
		public string REA_CapitaleSociale { get; set; }
		public string REA_SocioUnico { get; set; }
		public string REA_StatoLiquidazione { get; set; }
		public string IBAN { get; set; }
		public string Trasmittente_Codice { get; set; }
		public string Trasmittente_Country { get; set; }
		public decimal Iva { get; set; }
		public int Progressivo { get; set; }
	}

	public class PaypalConfigViewModel
	{
		public int ID_PaypalConfig { get; set; }
		public Nullable<System.Guid> ID_Grest { get; set; }
		public string api_username { get; set; }
		public string api_password { get; set; }
		public string applicationid { get; set; }
		public string api_signature { get; set; }
		public bool Removed { get; set; }
		public string street { get; set; }
		public string cityname { get; set; }
		public string stateorprovince { get; set; }
		public string country { get; set; }
		public Nullable<int> postalcode { get; set; }
		public bool PayPalRicaricheAbilitate { get; set; }
		public decimal? PercentualeRicarico { get; set; }
		public decimal? FixedFee { get; set; }
		public string Valuta { get; set; }
		public string defaultPaymentMethod { get; set; }
	}

	public class StripeConfigViewModel
	{
		public int ID_StripeConfig { get; set; }
		public Nullable<System.Guid> ID_Grest { get; set; }
		public string Country { get; set; }
		public string BusinessType { get; set; }
		public Nullable<decimal> PercentualeRicarico { get; set; }
		public string Valuta { get; set; }
		public Nullable<decimal> FixedFee { get; set; }
		public bool Removed { get; set; }
		public bool StripeRicaricheAbilitate { get; set; }
		public string StripeAccountId { get; set; }
		public string defaultPaymentMethod { get; set; }
	}

	public class UserWebPlus : UserWeb
	{
		public string CittaNascita { get; set; }
		public string ProvinciaNascita { get; set; }

		public string Frequenza { get; set; }

		public UserWebPlus(UserWeb u)
		{
			this.Address = u.Address;
			this.Avatar = string.IsNullOrEmpty(u.Avatar) ? default(string) : u.Avatar;
			this.Barcode = u.Barcode;
			this.BarcodeArchive = u.BarcodeArchive;
			this.Birthday = u.Birthday;
			this.Cap = u.Cap;
			this.CapGenitoreFiscale = u.CapGenitoreFiscale;
			this.CfGenitoreFiscale = u.CfGenitoreFiscale;
			this.DescrGenitoreFiscale = u.DescrGenitoreFiscale;
			this.ChildCf = u.ChildCf;
			this.Citta = u.Citta;
			this.CittaGenitoreFiscale = u.CittaGenitoreFiscale;
			this.CognomeGenitoreFiscale = u.CognomeGenitoreFiscale;
			this.Conto = u.Conto;
			this.Country = u.Country;
			this.DateInsert = u.DateInsert;
			this.Email = u.Email;
			this.Enable = u.Enable;
			this.Father = u.Father;
			this.FatherPhone = u.FatherPhone;
			this.Gender = u.Gender;
			this.GrandparentsPhone = u.GrandparentsPhone;
			this.ID_Comune = u.ID_Comune;
			this.ID_Grest = u.ID_Grest;
			this.ID_Parent = u.ID_Parent;
			this.ID_Provincia = u.ID_Provincia;
			this.ID_User = u.ID_User;
			this.ID_UserType = u.ID_UserType;
			this.ID_Year = u.ID_Year;
			this.IndirizzoGenitoreFiscale = u.IndirizzoGenitoreFiscale;
			this.InsuranceN = u.InsuranceN;
			this.Lastname = u.Lastname;
			this.LimiteGiornalieroBar = u.LimiteGiornalieroBar;
			this.MedicalCare = u.MedicalCare;
			this.MedicalInfo = u.MedicalInfo;
			this.Mother = u.Mother;
			this.MotherPhone = u.MotherPhone;
			this.Name = u.Name;
			this.NomeGenitoreFiscale = u.NomeGenitoreFiscale;
			this.Note = u.Note;
			this.NoteRiservate = u.NoteRiservate;
			this.ParentCf = u.ParentCf;
			this.Phone = u.Phone;
			this.Provincia = u.Provincia;
			this.ProvinciaGenitoreFiscale = u.ProvinciaGenitoreFiscale;
			this.Pwd = u.Pwd;
			this.Rel = u.Rel;
			this.Removed = u.Removed;
			this.Ruolo = u.Ruolo;
			this.SchoolCity = u.SchoolCity;
			this.SchoolClass = u.SchoolClass;
			this.SchoolName = u.SchoolName;
			this.Size = u.Size;
			this.TempoPienoParziale = u.TempoPienoParziale;
			this.Weeks = u.Weeks;
			this.CittaNascita = new Comune(u.ID_Comune == null ? 0 : u.ID_Comune.Value).Nome;
			this.ProvinciaNascita = new Provincia(u.ID_Provincia == null ? 0 : u.ID_Provincia.Value).Nome;
			this.Extra1 = u.Extra1;
			this.Extra2 = u.Extra2;
			this.Extra3 = u.Extra3;
			this.Qualified = u.Qualified;
			this.CartaIdentita = u.CartaIdentita;
			this.TesseraSanitaria = u.TesseraSanitaria;
			this.TripDiscount = u.TripDiscount;
			this.TripDiscountType = u.TripDiscountType;
			this.CardN = u.CardN;
			this.ID_Headquarter = u.ID_Headquarter;
			this.DateExpiration = u.DateExpiration;
			this.DateExpirationMedicalExam = u.DateExpirationMedicalExam;
			this.DataBattesimo = u.DataBattesimo;
			this.LuogoBattesimo = u.LuogoBattesimo;
			this.DataComunione = u.DataComunione;
			this.LuogoComunione = u.LuogoComunione;
			this.DataCresima = u.DataCresima;
			this.LuogoCresima = u.LuogoCresima;
			this.CelebranteCresima = u.CelebranteCresima;
			this.IndirizzoPadrino = u.IndirizzoPadrino;
			this.IndirizzoMadrina = u.IndirizzoMadrina;
			this.NominativoPadrino = u.NominativoPadrino;
			this.NominativoMadrina = u.NominativoMadrina;
			this.BattesimoAltraParrocchia = u.BattesimoAltraParrocchia;
			this.BattesimoFuoriCitta = u.BattesimoFuoriCitta;
			this.BattesimoNullaOsta = u.BattesimoNullaOsta;
			this.BattesimoOrtodosso = u.BattesimoOrtodosso;
			this.LastLoginDate = u.LastLoginDate;
			this.Groups = u.Groups;
			this.Activities = u.Activities;
		}
	}
}
