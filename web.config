<?xml version="1.0" encoding="UTF-8"?>
<!--
  Per ulteriori informazioni su come configurare l'applicazione ASP.NET, visitare il sito Web all'indirizzo
  http://go.microsoft.com/fwlink/?LinkId=169433
  -->
<configuration>
  <configSections>
    <!-- Setup the Piczard configuration sections -->
    <sectionGroup name="codeCarvings.piczard">
      <section name="coreSettings" type="CodeCarvings.Piczard.Configuration.CoreSettingsSectionHandler, CodeCarvings.Piczard" requirePermission="false" />
      <section name="webSettings" type="CodeCarvings.Piczard.Configuration.WebSettingsSectionHandler, CodeCarvings.Piczard" requirePermission="false" />
    </sectionGroup>
    <!-- NHibernate -->
    <section name="hibernate-configuration" type="NHibernate.Cfg.ConfigurationSectionHandler, NHibernate" />
    <section name="paypal" type="PayPal.Manager.SDKConfigH<PERSON><PERSON>, PayPalCoreSDK" />
  </configSections>
  
    <system.web.extensions>
    <scripting>
      <webServices>
        <jsonSerialization maxJsonLength="2147483647" />
      </webServices>
    </scripting>
  </system.web.extensions>

  
  <!-- NHibernate -->
  <hibernate-configuration xmlns="urn:nhibernate-configuration-2.2">
    <session-factory>
      <property name="connection.driver_class">NHibernate.Driver.SqlClientDriver</property>
      <property name="connection.connection_string">Data Source=.\SQLEXPRESS,1433;Initial Catalog=iGrest;User ID=igrest_app;Password=iGrest2025!Secure;MultipleActiveResultSets=True;Connect Timeout=30;Encrypt=False;TrustServerCertificate=True</property>
      <property name="dialect">NHibernate.Dialect.CustomSql2008Dialect, App_code</property>
      <property name="query.substitutions">true=1;false=0</property>
      <property name="proxyfactory.factory_class">NHibernate.ByteCode.LinFu.ProxyFactoryFactory, NHibernate.ByteCode.LinFu</property>
      <property name="current_session_context_class">web</property>
      <!--<property name="use_sql_comments">false</property>
      <property name="show_sql">true</property>
	  <property name="cache.use_query_cache">true</property>-->
    </session-factory>
  </hibernate-configuration>

  <!-- Piczard configuration sections -->
  <codeCarvings.piczard>
    <coreSettings> 
      <drawing maxImageSize="5000, 5000" />
      <imageArchiver defaultJpegQuality="92" defaultQuantizeGifImages="true" />
      <security defaultEncryptionKey="Place here your random key (alphanumeric / 10..20 chars)" />
    </coreSettings>
    <webSettings>
      <webResource enableCompression="true" />
      <!-- Set useTemporaryFiles to false if you don't want let Piczard ceate temporary files  -->
      <pictureTrimmer useTemporaryFiles="true" autoDeleteTemporaryFiles="false" />
      <temporaryFileManager folderPath="~/App_Data/PiczardTempFiles" autoDeleteFiles="true" maxFileLifeTime="180" />
    </webSettings>
  </codeCarvings.piczard>

  <!-- Allow * Access to the Piczard HttpHandler (required when using Asp.Net Authentication) -->
  <location path="piczardWebResource.ashx">
    <system.web>
      <authorization>
        <allow users="*" />
      </authorization>
    </system.web>
  </location>
  <location path="ajax/loginws.asmx">
    <system.web>
      <authorization>
        <allow users="*" />
      </authorization>
    </system.web>
  </location>
  <location path="ajax/geows.asmx">
    <system.web>
      <authorization>
        <allow users="*" />
      </authorization>
    </system.web>
  </location>
  <location path="ajax/GrestIscrizioneTypeWs.asmx">
    <system.web>
      <authorization>
        <allow users="*" />
      </authorization>
    </system.web>
  </location>
  <location path="ajax">
    <system.web>
      <authorization>
        <allow users="*" />
      </authorization>
    </system.web>
  </location>
  <appSettings>
    <!-- slider img -->
    <add key="banner_width" value="697" />
    <add key="banner_height" value="435" />
    <!-- box img -->
    <add key="box_width" value="278" />
    <add key="box_height" value="141" />
    <!-- prodotti -->
    <add key="pro_cart_min_w" value="40" />
    <add key="pro_cart_min_h" value="40" />
    <add key="pro_cart_item_w" value="82" />
    <add key="pro_cart_item_h" value="82" />
    <add key="pro_gallery_thumb_w" value="85" />
    <add key="pro_gallery_thumb_h" value="85" />
    <add key="pro_gallery_item_w" value="360" />
    <add key="pro_gallery_item_h" value="414" />
    <add key="pro_gallery_big_w" value="1029" />
    <add key="pro_gallery_big_h" value="1200" />
    <add key="pro_list_w" value="220" />
    <add key="pro_list_h" value="242" />
    <!-- pagine -->
    <add key="nav_pag_w" value="700" />
    <add key="nav_pag_h" value="322" />
    <!-- news -->
    <add key="nws_list_w" value="640" />
    <add key="nws_list_h" value="480" />
    <!-- loghi -->
    <add key="logo_list_w" value="340" />
    <add key="logo_list_h" value="120" />
	<!-- firma -->
	<add key="firma_list_w" value="640" />
    <add key="firma_list_h" value="480" />
    <!-- Categoria -->
    <add key="cat_pro_w" value="220" />
    <add key="cat_pro_h" value="175" />
    <add key="cat_banner_w" value="760" />
    <add key="cat_banner_h" value="175" />
    <add key="cat_img_default" value="/images/logo.png" />
    <!-- user -->
    <add key="user_thumb_w" value="46" />
    <add key="user_thumb_h" value="46" />
    <add key="user_w" value="200" />
    <add key="user_h" value="200" />
	
	 <!--Product-->
    <add key="prod_thumb_w" value="46" />
    <add key="prod_thumb_h" value="46" />
    <add key="prod_w" value="340" />
	<add key="prod_h" value="340" />
	
	 <add key="PathDocs" value="C:\websites\squby.it.live\upload" />
	 <add key="SmtpEmail" value="pro.eu.turbo-smtp.com" />
     <add key="SmtpPort" value="465" />
     <add key="SmtpSsl" value="true" />
     <add key="SendfromEmail" value="<EMAIL>" />
     <add key="SendfromPwd" value="Dotti!2025!" />
     <add key="ContactFormTo" value="<EMAIL>" />
    <add key="PayPalMode" value="live" />
    <add key="PayPalHost" value="https://www.paypal.com/cgi-bin/webscr?cmd=_express-checkout&amp;token=" />
    <add key="pagamentoUrlPaypal" value="http://live.igrest.it/admin/grest_payment.aspx" />
    <add key="pagamentoUrlBack" value="http://live.igrest.it/admin/grest_payment.aspx" />
	<add key="iscrizioneUrlPaypal" value="http://live.igrest.it/iscrizione.aspx" />
    <add key="iscrizioneUrlBack" value="http://live.igrest.it/iscrizione.aspx" />
  </appSettings>
  
  <!-- DAVIDE -->  
  <connectionStrings>
    <add name="ConnectionString" connectionString="Data Source=.\SQLEXPRESS,1433;Initial Catalog=iGrest;User ID=igrest_app;Password=iGrest2025!Secure;MultipleActiveResultSets=True;Connect Timeout=30;Encrypt=False;TrustServerCertificate=True" providerName="System.Data.SqlClient" />
    <add name="iGrestEntities" connectionString="metadata=res://*/iGrestDBModel.csdl|res://*/iGrestDBModel.ssdl|res://*/iGrestDBModel.msl;provider=System.Data.SqlClient;provider connection string=&quot;Data Source=.\SQLEXPRESS,1433;Initial Catalog=iGrest;User ID=igrest_app;Password=iGrest2025!Secure;MultipleActiveResultSets=True;Connect Timeout=30;Encrypt=False;TrustServerCertificate=True&quot;" providerName="System.Data.EntityClient" />
  </connectionStrings>
  
  
  <system.web>
    <sessionState timeout="30"></sessionState>
    <pages enableEventValidation="false"></pages>
    <globalization uiCulture="it-IT" culture="it-IT" />    
    <customErrors mode="Off"></customErrors>
    <authentication mode="Forms">
      <forms loginUrl="/admin/login.aspx" defaultUrl="/admin/dashboard.aspx" cookieless="UseCookies" name="_admin_user" slidingExpiration="true" timeout="30" />
    </authentication>
    <compilation debug="true" targetFramework="4.0" />
    <httpRuntime maxRequestLength="4000" requestValidationMode="4.0" maxQueryStringLength="2097151" />
    <httpModules>
      <add name="NHibernateSessionModule" type="SessionHelper, App_code" />
    </httpModules>
    <httpHandlers>
      <!-- Add the Piczard HttpHandler -->
      <add verb="*" path="piczardWebResource.ashx" validate="false" type="CodeCarvings.Piczard.Web.WebResourceManager, CodeCarvings.Piczard" />
    </httpHandlers>
    <webServices>
      <protocols>
        <add name="HttpGet" />
        <add name="HttpPost" />
      </protocols>
    </webServices>
  </system.web>
  <system.webServer>
    <staticContent>
      <!--<mimeMap fileExtension=".woff" mimeType="application/font-woff" />-->
      <!--<mimeMap fileExtension=".woff2" mimeType="application/font-woff2" />-->
            <!--<mimeMap fileExtension=".json" mimeType="application/json" />-->
            <!--<mimeMap fileExtension=".svg" mimeType="image/svg+xml" />-->
    </staticContent>
    <modules runAllManagedModulesForAllRequests="true" />
    <validation validateIntegratedModeConfiguration="false" />
    <handlers>
            <remove name="PHP-7.0.11" />
            <remove name="PHP-5.5.37" />
            <remove name="PHP" />
            <remove name="PHP-5.6.26" />
            <add name="JSON" path=".json" verb="*" modules="IsapiModule" scriptProcessor="C:\WINDOWS\system32\inetsrv\asp.dll" resourceType="Unspecified" preCondition="bitness64" />
      <!--<add name="UrlRewriting_aspx" path="*.aspx" verb="*" preCondition="integratedMode" type="UrlRewriting"/>-->
      <!-- Add the Piczard HttpHandler -->
      <add name="PiczardWebResource" verb="*" path="piczardWebResource.ashx" preCondition="integratedMode" type="CodeCarvings.Piczard.Web.WebResourceManager, CodeCarvings.Piczard" />
    </handlers>
        <defaultDocument>
            <files>
                <add value="login.aspx" />
            </files>
        </defaultDocument>
		</system.webServer>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="PayPalCoreSDK" publicKeyToken="5b4afc1ccaef40fb" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>
